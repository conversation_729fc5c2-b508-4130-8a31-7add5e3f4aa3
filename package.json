{"name": "swari-taxi-booking-app", "version": "1.0.0", "description": "Backend for swari Taxi Booking App", "type": "module", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dependencies": {"@adminjs/express": "^6.1.1", "@adminjs/mongoose": "^4.1.0", "@elastic/elasticsearch": "^8.17.1", "adminjs": "^7.8.15", "bcrypt": "^5.1.1", "chart.js": "^4.4.8", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-formidable": "^1.2.0", "express-session": "^1.18.1", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "mqtt": "^5.3.3", "qrcode": "^1.5.4", "react-chartjs-2": "^5.3.0", "speakeasy": "^2.0.0", "swari-taxi-booking-app": "file:", "tslib": "^2.8.1", "winston": "^3.11.0"}, "devDependencies": {"jest": "^29.7.0", "mongodb-memory-server": "^9.1.3", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=22.0.0"}, "jest": {"testEnvironment": "node", "testMatch": ["**/tests/**/*.test.js"], "collectCoverage": true, "coverageDirectory": "coverage"}}