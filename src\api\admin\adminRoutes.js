/**
 * Admin Routes
 * Defines routes for admin authentication and dashboard endpoints
 * PRD Reference: Sections 4.11, 10.2
 */

import express from 'express';
const router = express.Router();
import { getDashboardData, getAllUsers, updateUserStatus, getAllTrips, getAllTransactions, registerAdmin, changePassword} from './adminController.js';
import { check } from 'express-validator';
import { verifyToken, validateRequest } from '../auth/authMiddleware.js';

// Middleware to verify admin authentication
// const verifyAdmin = authMiddleware.verifyToken(['admin', 'super_admin']);

router.get('/test', (req, res) => {
  return res.status(200).json({ message: 'Admin routes working'});
})

/**
 * @route GET /api/admin/dashboard
 * @desc Get admin dashboard data
 * @access Admin only
 */

router.get('/dashboard', verifyToken, getDashboardData);

/**
 * @route GET /api/admin/users
 * @desc Get all users (customers and drivers)
 * @access Admin only
 */
router.get('/users', verifyToken, getAllUsers);

/**
 * @route PUT /api/admin/users/:id/status
 * @desc Update user status (suspend/activate)
 * @access Admin only
 */
router.put('/users/:id/status', verifyToken, updateUserStatus);

/**
 * @route GET /api/admin/trips
 * @desc Get all trips with filters
 * @access Admin only
 */
router.get('/trips', verifyToken, getAllTrips);

/**
 * @route GET /api/admin/transactions
 * @desc Get all transactions with filters
 * @access Admin only
 */
router.get('/transactions', verifyToken, getAllTransactions);

/**
 * @route POST /api/admin/register
 * @desc Register a new admin
 * @access Admin only (super_admin can create admin)
 */
router.post('/register',
  [
    check('username', 'Username is required')
      .notEmpty()
      .withMessage('Username is required')
      .isLength({ min: 3, max: 30 })
      .withMessage('Username must be between 3 and 30 characters')
      .matches(/^[a-zA-Z0-9_]+$/)
      .withMessage('Username can only contain letters, numbers, and underscores'),
    check('email', 'Please enter a valid email')
      .isEmail()
      .withMessage('Please enter a valid email address')
      .normalizeEmail(),
    check('password', 'Password is required')
      .isLength({ min: 8 })
      .withMessage('Password must be at least 8 characters long')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
      .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
    check('role')
      .optional()
      .isIn(['admin', 'super_admin'])
      .withMessage('Role must be either admin or super_admin')
  ],
  validateRequest,
  verifyToken,
  registerAdmin
);

/**
 * @route PUT /api/admin/change-password
 * @desc Change admin password
 * @access Admin only
 */
router.put('/change-password',
  [
    check('currentPassword', 'Current password is required')
      .notEmpty()
      .withMessage('Current password is required'),
    check('newPassword', 'New password is required')
      .isLength({ min: 8 })
      .withMessage('New password must be at least 8 characters long')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
      .withMessage('New password must contain at least one lowercase letter, one uppercase letter, and one number')
  ],
  validateRequest,
  verifyToken,
  changePassword
);

// Mount log analytics routes
// router.use('/logs', logAnalyticsRoutes);

export default router;