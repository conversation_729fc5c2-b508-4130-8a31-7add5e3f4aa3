/**
 * Minimal AdminJS Configuration for Testing
 */

import AdminJS from 'adminjs';
import AdminJSExpress from "@adminjs/express";
import bcrypt from 'bcrypt';
import mongoose from 'mongoose';

// Import models
import Admin from '../../../models/Admin.js';
import Log from '../../../models/Log.js';

// Import utilities
import logger from '../../../utils/logger.js';
import config from '../../../config/config.js';

// Register adapter
const registerAdapter = async () => {
  try {
    console.log('Registering AdminJS Mongoose adapter...');
    const AdminJSMongoose = await import('@adminjs/mongoose');
    
    AdminJS.registerAdapter({
      Database: AdminJSMongoose.Database,
      Resource: AdminJSMongoose.Resource,
    });
    console.log('AdminJS Mongoose adapter registered successfully');
  } catch (error) {
    console.error('Failed to register AdminJS adapter:', error);
    throw error;
  }
};

/**
 * Configure minimal AdminJS
 */
const configureMinimalAdminJS = async (app) => {
  try {
    console.log('Starting minimal AdminJS configuration...');
    
    // Register adapter
    await registerAdapter();
    
    // Create minimal AdminJS instance
    const adminJs = new AdminJS({
      resources: [
        {
          resource: Admin,
          options: {
            navigation: {
              name: 'User Management',
              icon: 'User',
            },
          },
        }
      ],
      rootPath: '/admin',
      branding: {
        companyName: 'Swari Taxi Admin',
        logo: false,
        softwareBrothers: false,
      },
    });
    
    console.log('AdminJS instance created');
    
    // Create authenticated router
    const router = AdminJSExpress.buildAuthenticatedRouter(
      adminJs,
      {
        authenticate: async (email, password) => {
          console.log('=== AUTHENTICATE FUNCTION CALLED ===');
          console.log('Authentication attempt for email:', email);
          
          try {
            const admin = await Admin.findOne({ email });
            
            if (!admin) {
              console.log('Authentication failed: Admin not found');
              return false;
            }
            
            if (admin.account_locked) {
              console.log('Authentication failed: Account locked');
              return false;
            }
            
            const matched = await bcrypt.compare(password, admin.password);
            
            if (matched) {
              console.log('Authentication successful for:', email);
              return admin;
            }
            
            console.log('Authentication failed: Password mismatch');
            return false;
          } catch (error) {
            console.log('Authentication error:', error.message);
            return false;
          }
        },
        cookiePassword: config.adminJSCookiesSecret,
      },
      null,
      {
        resave: false,
        saveUninitialized: true,
        secret: config.adminJSSessionSecret,
        cookie: {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          maxAge: 60 * 60 * 1000,
        },
        name: 'adminjs',
      }
    );
    
    console.log('AdminJS router created');
    
    // Mount router
    app.use(adminJs.options.rootPath, router);
    console.log('AdminJS mounted at:', adminJs.options.rootPath);
    
    return adminJs;
  } catch (error) {
    console.error('Error in minimal AdminJS configuration:', error);
    throw error;
  }
};

export default configureMinimalAdminJS;
