/**
 * AdminJS Configuration
 * Sets up AdminJS with authentication, resources, and components
 * PRD Reference: Sections 4.11, 10.2
 */

// const AdminJS = require('adminjs');
import AdminJS, {ComponentLoader} from 'adminjs';
// const AdminJSExpress = require('@adminjs/express');
import AdminJSExpress from "@adminjs/express";
// const AdminJSMongoose = require('@adminjs/mongoose');
// console.trace("trace 1");


// const bcrypt = require('bcrypt');
import bcrypt from 'bcrypt';
// const session = require('express-session');
import session from 'express-session'
// const mongoose = require('mongoose');
import mongoose from 'mongoose';

// Import models
import Admin from '../../../models/Admin.js';
import Log from '../../../models/Log.js';

// Import resources
import resources from './resources/index.js';

// Import utilities
import logger from '../../../utils/logger.js';
import config from '../../../config/config.js';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);



const registerAdminJsAdapterHandler = async() => {
  try {
    console.log('=== REGISTERING ADMINJS MONGOOSE ADAPTER ===');
    const AdminJSMongoose  = await import('@adminjs/mongoose');

    // Register the mongoose adapter
    AdminJS.registerAdapter({
      Database: AdminJSMongoose.Database,
      Resource: AdminJSMongoose.Resource,
    });
    console.log('=== ADMINJS MONGOOSE ADAPTER REGISTERED SUCCESSFULLY ===');
  } catch (error) {
    console.error('=== ERROR REGISTERING ADMINJS ADAPTER ===', error);
    throw error;
  }
}

// Register adapter - this will be awaited in the configureAdminJS function

/**
 * Configure AdminJS
 * @param {Object} app - Express app
 */
const configureAdminJS = async (app) => {
  // Register the adapter first
  await registerAdminJsAdapterHandler();

  const componentLoader = new ComponentLoader();
  const enhancedDashboardComponent = componentLoader.add(
    'enhancedDashboard', 
    join(__dirname, './components/enhanced-dashboard') // Path to your React component
  );
 
  const logViewerComponent = componentLoader.add(
    'logViewer', 
    join(__dirname, './components/log-viewer') // Path to your React component
  );
  const logAnalyticsComponent = componentLoader.add(
    'logAnalytics', 
    join(__dirname, './components/log-analytics') // Path to your React component
  );
  // AdminJS.bundle('./components/log-analytics')
  
  // Define custom dashboard
  const dashboard = {
    //component: AdminJS.bundle('./components/dashboard')
    component:  enhancedDashboardComponent
  };

  // Define custom branding
  const branding = {
    companyName: 'Swari Taxi Admin',
    logo: false,
    softwareBrothers: false,
    favicon: '/favicon.ico'
  };

  // Define custom theme
  const theme = {
    colors: {
      primary100: '#4268F6',
      primary80: '#5C7DF7',
      primary60: '#7693F8',
      primary40: '#90A8F9',
      primary20: '#ABBEFA',
      grey100: '#151C38',
      grey80: '#424D6A',
      grey60: '#68718D',
      grey40: '#ACAEBF',
      grey20: '#F0F0F7',
      filterBg: '#4268F6',
      accent: '#38CAF1',
      hoverBg: '#4268F6',
    },
  };
  
  // Define custom pages
  const pages = {
    logViewer: {
      component: logViewerComponent,
      handler: async (request, response, context) => {
        return { message: 'Log Viewer Page' };
      },
    },
    logAnalytics: {
      component: logAnalyticsComponent,
      handler: async (request, response, context) => {
        return { message: 'Log Analytics Page' };
      },
    },
  };

  // Create AdminJS instance
  const adminJs = new AdminJS({
    resources,
    dashboard,
    branding,
    theme,
    pages,
    rootPath: '/admin',
  });
  // Remove the middleware that was intercepting requests
  // The logging will be done inside the authenticate function instead

  // Setup authentication with comprehensive logging
  console.log('=== CREATING ADMINJS AUTHENTICATED ROUTER ===');

  // Create session configuration
  const sessionOptions = {
    resave: false,
    saveUninitialized: true,
    secret: config.adminJSSessionSecret,
    cookie: {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 60 * 60 * 1000, // 1 hour
    },
    name: 'adminjs',
  };

  const router = AdminJSExpress.buildAuthenticatedRouter(
    adminJs,
    {
      authenticate: async (email, password) => {
        console.log('=== AUTHENTICATE FUNCTION CALLED ===');
        console.log('Authentication attempt for email:', email);

        try {
          logger.info('AdminJS authentication attempt:', {email});

          const admin = await Admin.findOne({ email });

          if (!admin) {
            console.log('Authentication failed: Admin not found');
            return false;
          }

          if (admin.account_locked) {
            console.log('Authentication failed: Account locked');
            return false;
          }

          const matched = await bcrypt.compare(password, admin.password);

          if (matched) {
            console.log('Authentication successful for:', email);
            // Reset login attempts on successful login
            admin.login_attempts = 0;
            admin.last_login = new Date();
            await admin.save();

            // Log successful login
            const log = new Log({
              event_type: 'admin_action',
              user_id: admin._id,
              user_type: 'Admin',
              details: { action: 'login', success: true },
              ip_address: '127.0.0.1' // In a real app, get the actual IP
            });
            await log.save();

            return admin;
          }

          console.log('Authentication failed: Password mismatch');
          // Increment login attempts on failed login
          admin.login_attempts += 1;

          // Lock account after 5 failed attempts
          if (admin.login_attempts >= 5) {
            admin.account_locked = true;
            logger.warn('Admin account locked due to too many failed login attempts', { adminId: admin._id });
          }

          await admin.save();

          // Log failed login
          const log = new Log({
            event_type: 'admin_action',
            user_id: admin._id,
            user_type: 'Admin',
            details: { action: 'login', success: false },
            ip_address: '127.0.0.1' // In a real app, get the actual IP
          });
          await log.save();

          return false;
        } catch (error) {
          console.log('Authentication error:', error.message);
          logger.error('Error during admin authentication', { error: error.message });
          return false;
        }
      },
      cookiePassword: config.adminJSCookiesSecret,
    },
    null,
    sessionOptions
  );

  console.log('=== ADMINJS ROUTER CREATED ===');

  // Add middleware to the AdminJS router to test if it's working
  router.use((req, res, next) => {
    console.log('=== ADMINJS ROUTER MIDDLEWARE HIT ===');
    console.log('AdminJS router processing:', req.method, req.path, req.originalUrl);
    next();
  });

  // Debug: Log router stack to see what routes are available
  console.log('=== ADMINJS ROUTER STACK DEBUG ===');
  console.log('Router stack length:', router.stack.length);

  router.stack.forEach((layer, index) => {
    if (layer.route) {
      console.log(`Route ${index}: ${layer.route.path} [${Object.keys(layer.route.methods).join(', ')}]`);
    } else {
      console.log(`Middleware ${index}: ${layer.name || 'anonymous'} - ${layer.regexp.toString()}`);
    }
  });

  // Mount AdminJS router
  console.log('=== MOUNTING ADMINJS ROUTER ===');
  console.log('AdminJS root path:', adminJs.options.rootPath);

  logger.info('AdminJS router being mounted at path:', { rootPath: adminJs.options.rootPath });
  app.use(adminJs.options.rootPath, router);
  console.log('=== ADMINJS ROUTER MOUNTED SUCCESSFULLY ===');

  return adminJs;
};

export default configureAdminJS;

  
