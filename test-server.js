/**
 * Minimal test server to debug startup issues
 */

console.log('Starting test server...');

try {
  console.log('Importing express...');
  import('express').then(express => {
    console.log('Express imported successfully');
    
    const app = express.default();
    
    app.get('/', (req, res) => {
      res.json({ message: 'Test server working' });
    });
    
    const PORT = 3001;
    app.listen(PORT, () => {
      console.log(`Test server running on port ${PORT}`);
    });
  }).catch(error => {
    console.error('Error importing express:', error);
  });
} catch (error) {
  console.error('Error in test server:', error);
}
